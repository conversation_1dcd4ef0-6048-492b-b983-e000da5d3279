.progressContainer {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  padding: 2rem;
  border-radius: 20px;
  box-shadow:
    0 10px 25px rgba(0, 0, 0, 0.08),
    0 0 0 1px rgba(255, 255, 255, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 0;
  position: relative;
}

.progressContainer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
}

.stepsWrapper {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
  width: 100%;
}

.stepContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
}

.stepIndicator {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
  justify-content: center;
}

.stepCircle {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
  font-weight: 700;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
  border: 3px solid transparent;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stepCircle.completed {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border-color: #10b981;
  box-shadow: 0 4px 16px rgba(16, 185, 129, 0.4);
}

.stepCircle.current {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border-color: #10b981;
  box-shadow:
    0 0 0 4px rgba(16, 185, 129, 0.2),
    0 4px 16px rgba(16, 185, 129, 0.4);
  transform: scale(1.1);
}

.stepCircle.pending {
  background: linear-gradient(145deg, #e2e8f0 0%, #cbd5e1 100%);
  color: #64748b;
  border-color: #cbd5e1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.connectionLine {
  position: absolute;
  top: 50%;
  left: 50%;
  right: -50%;
  height: 6px;
  transform: translateY(-50%);
  z-index: 1;
  transition: all 0.3s ease;
  border-radius: 3px;
}

.lineCompleted {
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

.linePending {
  background: linear-gradient(90deg, #e2e8f0 0%, #cbd5e1 100%);
}

.stepLabel {
  margin-top: 1rem;
  text-align: center;
  min-height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stepTitle {
  font-size: 0.8rem;
  font-weight: 600;
  line-height: 1.3;
  text-align: center;
  transition: all 0.3s ease;
  max-width: 90px;
  letter-spacing: 0.025em;
}

.titleCompleted {
  color: #059669;
  font-weight: 700;
}

.titleCurrent {
  color: #059669;
  font-weight: 700;
  transform: scale(1.05);
}

.titlePending {
  color: #94a3b8;
  font-weight: 500;
}

/* Responsive design */
@media (max-width: 768px) {
  .progressContainer {
    padding: 1rem;
  }

  .stepCircle {
    width: 32px;
    height: 32px;
    font-size: 0.875rem;
  }

  .stepTitle {
    font-size: 0.7rem;
    max-width: 60px;
  }

  .connectionLine {
    height: 3px;
  }
}

@media (max-width: 480px) {
  .stepCircle {
    width: 28px;
    height: 28px;
    font-size: 0.75rem;
  }

  .stepTitle {
    font-size: 0.65rem;
    max-width: 50px;
  }

  .connectionLine {
    height: 2px;
  }

  .stepLabel {
    margin-top: 0.5rem;
    min-height: 2rem;
  }
}
