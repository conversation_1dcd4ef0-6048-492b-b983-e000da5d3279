'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import styles from './planner.module.css';
import PlannerStep from '@/components/PlannerStep';
import SelectionOptions from '@/components/SelectionOptions';
import ProgressBar from '@/components/ProgressBar';
import plannersData from '../data/planners.json';

interface PlannerSelection {
  layout: string;
  color: string;
  year: string;
  weekStart: string;
  weeklyLayout: string;
  dailyLayout: string;
}

interface PlannerStep {
  number: number;
  title: string;
  description: string;
  options: string[];
  key: keyof PlannerSelection;
}

const plannerSteps: PlannerStep[] = [
  {
    number: 1,
    title: 'Choose Layout',
    description: 'Select between Landscape or Portrait orientation.',
    options: ['Landscape', 'Portrait'],
    key: 'layout'
  },
  {
    number: 2,
    title: 'Select Color',
    description: 'Choose between Rainbow or Black version.',
    options: ['Rainbow', 'Black'],
    key: 'color'
  },
  {
    number: 3,
    title: 'Pick Year',
    description: 'Select between Undated, 2025, or 2026.',
    options: ['Undated', '2025', '2026'],
    key: 'year'
  },
  {
    number: 4,
    title: 'Week Start',
    description: 'Choose between Monday or Sunday start.',
    options: ['Monday', 'Sunday'],
    key: 'weekStart'
  },
  {
    number: 5,
    title: 'Weekly Layout',
    description: 'Select your preferred weekly layout style.',
    options: ['W1', 'W2', 'W3', 'W4', 'W5'],
    key: 'weeklyLayout'
  },
  {
    number: 6,
    title: 'Daily Layout',
    description: 'Choose your preferred daily layout style.',
    options: ['D1', 'D2', 'D3', 'D4', 'D5'],
    key: 'dailyLayout'
  }
];

const initialSelections: PlannerSelection = {
  layout: '',
  color: '',
  year: '',
  weekStart: '',
  weeklyLayout: '',
  dailyLayout: ''
};

const STORAGE_KEY = 'planner-selections';

export default function PlannerPage() {
  const [selectedStep, setSelectedStep] = useState<number>(1);
  const [selections, setSelections] = useState<PlannerSelection>(initialSelections);
  const [selectedPlanner, setSelectedPlanner] = useState<typeof plannersData[0] | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [imageError, setImageError] = useState(false);
  const [isImageLoading, setIsImageLoading] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);
  const resultRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setIsMounted(true);
    // Clear localStorage on page load to reset selections
    localStorage.removeItem(STORAGE_KEY);
    setSelections(initialSelections);
    setSelectedStep(1);
    setSelectedPlanner(null);
    setError(null);
  }, []);

  useEffect(() => {
    if (isMounted) {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(selections));
    }
  }, [selections, isMounted]);

  useEffect(() => {
    if (!isMounted || !selectedPlanner) return;

    const imagePath = `/images/${selections.layout}/${selections.color}/${selections.year}/${selections.weekStart}/${selections.weeklyLayout}/${selections.dailyLayout}.webp`;
    setIsImageLoading(true);

    const img = new window.Image();
    img.src = imagePath;

    img.onload = () => {
      setIsImageLoading(false);
    };

    img.onerror = () => {
      setImageError(true);
      setIsImageLoading(false);
    };
  }, [selectedPlanner, selections, isMounted]);

  // Helper function to check if a step is completed
  const isStepCompleted = useCallback((stepNumber: number): boolean => {
    const step = plannerSteps[stepNumber - 1];
    return selections[step.key] !== '';
  }, [selections]);

  // Helper function to check if a step is locked
  const isStepLocked = useCallback((stepNumber: number): boolean => {
    // A step is locked if any previous step is not completed
    for (let i = 1; i < stepNumber; i++) {
      if (!isStepCompleted(i)) {
        return true;
      }
    }
    return false;
  }, [isStepCompleted]);

  // Helper function to get the next available step
  const getNextAvailableStep = useCallback((): number => {
    for (let i = 1; i <= plannerSteps.length; i++) {
      if (!isStepCompleted(i)) {
        return i;
      }
    }
    return plannerSteps.length; // All steps completed
  }, [isStepCompleted]);

  // Helper function to count completed steps
  const getCompletedStepsCount = useCallback((): number => {
    return plannerSteps.filter((_, index) => isStepCompleted(index + 1)).length;
  }, [isStepCompleted]);

  // Update selected step when selections change
  useEffect(() => {
    const nextStep = getNextAvailableStep();
    if (nextStep !== selectedStep) {
      setSelectedStep(nextStep);
    }
  }, [getNextAvailableStep, selectedStep]);

  // Auto-scroll to result when planner is selected
  useEffect(() => {
    if (selectedPlanner && resultRef.current) {
      // Add a small delay to ensure the result section is rendered
      setTimeout(() => {
        resultRef.current?.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest'
        });
      }, 300);
    }
  }, [selectedPlanner]);

  const handleOptionSelect = (option: string) => {
    const currentStep = plannerSteps[selectedStep - 1];
    const key = currentStep.key;

    const newSelections = {
      ...selections,
      [key]: option
    };

    setSelections(newSelections);

    // Check if all steps are completed after this selection
    const allCompleted = plannerSteps.every((step, index) => {
      if (index + 1 === selectedStep) {
        return option !== ''; // Current step will be completed with this selection
      }
      return newSelections[step.key] !== '';
    });

    if (allCompleted) {
      // All steps completed, find matching planner
      const fullPath = `${newSelections.layout} ${newSelections.color} ${newSelections.year} ${newSelections.weekStart} ${newSelections.weeklyLayout} ${newSelections.dailyLayout}`;
      const match = plannersData.find(item => item.path === fullPath);

      if (match) {
        setSelectedPlanner(match);
        setError(null);
      } else {
        setError('No matching planner found. Please try different selections.');
        setSelectedPlanner(null);
      }
    }
    // Note: selectedStep will be updated automatically by the useEffect that watches selections
  };

  const handleReset = () => {
    setSelections(initialSelections);
    setSelectedStep(1);
    setSelectedPlanner(null);
    setError(null);
    setImageError(false);
    setIsImageLoading(false);
    localStorage.removeItem(STORAGE_KEY);
  };

  const handleGoBackToStep = (stepNumber: number) => {
    // Clear all selections from the target step onwards
    const newSelections = { ...selections };
    for (let i = stepNumber; i <= plannerSteps.length; i++) {
      const step = plannerSteps[i - 1];
      newSelections[step.key] = '';
    }
    setSelections(newSelections);
    setSelectedPlanner(null);
    setError(null);
    setImageError(false);
    setIsImageLoading(false);
  };

  const handleCopyLink = async () => {
    if (selectedPlanner) {
      try {
        await navigator.clipboard.writeText(selectedPlanner.Link);
        setCopySuccess(true);
        setTimeout(() => setCopySuccess(false), 2000);
      } catch (err) {
        console.error('Failed to copy:', err);
      }
    }
  };

  const getOptionImagePath = (option: string) => {
    switch (selectedStep) {
      case 1: // Layout
        return `/images/Select Layout/${option}.webp`;

      case 2: // Color
        return `/images/Select Color/${option}/${selections.layout}/${selections.layout} ${option}.webp`;

      case 3: // Year
        return `/images/Select Year/${option}/${selections.color}/${selections.layout}/${selections.layout} ${selections.color} ${option}.webp`;

      case 4: // Week Start
        return `/images/Select Week start/${option}/${selections.year}/${selections.color}/${selections.layout}/${selections.layout} ${selections.color} ${selections.year} ${option}.webp`;

      case 5: // Weekly Layout
        // const weeklyLayoutMap: Record<string, string> = {
        //   'W1': 'Detailed',
        //   'W2': 'Lined',
        //   'W3': 'Horizontal',
        //   'W4': 'Vertical',
        //   'W5': 'To Do List'
        // };
        return `/images/Select Weekly Layout/${[option]}/${selections.weekStart}/${selections.year}/${selections.color}/${selections.layout}/${selections.layout} ${selections.color} ${selections.year} ${selections.weekStart} ${option}.webp`;

      case 6: // Daily Layout
        return `/images/Select Daily Layout/${option}/${selections.year}/${selections.color}/${selections.layout}/${selections.layout} ${selections.color} ${selections.year} ${option}.webp`;

      default:
        return '';
    }
  };

  const getFinalImagePath = () => {
    if (!selections.layout) return '';

    const layout = selections.layout;
    const color = selections.color;
    const year = selections.year;
    const dailyLayout = selections.dailyLayout;

    return `/images/Select Daily Layout/${dailyLayout}/${year}/${color}/${layout}/${layout} ${color} ${dailyLayout} ${year}.webp`;
  };

  if (!isMounted) {
    return (
      <main className={styles.main}>
        <div className={styles.container}>
          <div className={styles.header}>
            <h1 className={styles.title}>Customize Your Planner</h1>
            <p className={styles.subtitle}>Loading...</p>
          </div>
        </div>
      </main>
    );
  }

  return (
    <main className={styles.main}>
      <motion.div
        className={styles.container}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <motion.div className={styles.header}>
          <motion.h1
            className={styles.title}
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            Customize Your Planner
          </motion.h1>
          <motion.p
            className={styles.subtitle}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            Follow the steps below to create your perfect planner
          </motion.p>
        </motion.div>

        <ProgressBar
          currentStep={selectedStep}
          totalSteps={plannerSteps.length}
          completedSteps={getCompletedStepsCount()}
          stepTitles={plannerSteps.map(step => step.title)}
        />

        <div className={styles.contentArea}>
          <div className={styles.mainContent}>
            <AnimatePresence mode="wait">
              <motion.div
                key={selectedStep}
                className={styles.preview}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
              >
            <div className={styles.stepCounter}>
              {getCompletedStepsCount() === plannerSteps.length ?
                'All Steps Complete!' :
                `Step ${selectedStep} of ${plannerSteps.length}`
              }
            </div>
            <motion.h2
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.1 }}
            >
              {plannerSteps[selectedStep - 1].title}
            </motion.h2>
            <motion.p
              className={styles.stepDescription}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2 }}
            >
              {plannerSteps[selectedStep - 1].description}
            </motion.p>
            {!isStepLocked(selectedStep) && (
              <SelectionOptions
                options={plannerSteps[selectedStep - 1].options}
                onSelect={handleOptionSelect}
                selectedOption={selections[plannerSteps[selectedStep - 1].key]}
                getOptionImagePath={getOptionImagePath}
              />
            )}
            {isStepLocked(selectedStep) && (
              <div className={styles.lockedMessage}>
                <p>Complete the previous steps to unlock this step.</p>
                <p className={styles.lockedHint}>
                  You need to make selections in the earlier steps before proceeding.
                </p>
              </div>
            )}
              </motion.div>
            </AnimatePresence>
          </div>

          {/* Sidebar with Current Selections Summary */}
          <div className={styles.sidebar}>
            {getCompletedStepsCount() > 0 && (
              <motion.div
                className={styles.selectionsummary}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <h3>Your Selections</h3>
                <div className={styles.selectionsList}>
                  {plannerSteps.map((step, index) => {
                    const stepNumber = index + 1;
                    const isCompleted = isStepCompleted(stepNumber);
                    const selection = selections[step.key];

                    if (!isCompleted) return null;

                    return (
                      <div key={step.number} className={styles.selectionItem}>
                        <span className={styles.selectionLabel}>{step.title}</span>
                        <span className={styles.selectionValue}>{selection}</span>
                        <button
                          className={styles.changeButton}
                          onClick={() => handleGoBackToStep(stepNumber)}
                          title={`Change ${step.title}`}
                        >
                          Change
                        </button>
                      </div>
                    );
                  })}
                </div>
              </motion.div>
            )}
          </div>
        </div>

        <AnimatePresence>
          {error && (
            <motion.div
              className={styles.error}
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
            >
              {error}
            </motion.div>
          )}
        </AnimatePresence>

        <AnimatePresence>
          {selectedPlanner && (
            <motion.div
              ref={resultRef}
              className={styles.result}
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              transition={{ duration: 0.3 }}
            >
              <motion.h2
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                Your Perfect Planner is Ready!
              </motion.h2>
              <div className={styles.plannerPreview}>
                {isImageLoading ? (
                  <motion.div
                    className={styles.loadingImage}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                  >
                    <div className={styles.loadingSpinner}></div>
                    <p>Loading preview...</p>
                  </motion.div>
                ) : !imageError ? (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3 }}
                    className={styles.imageContainer}
                  >
                    <Image
                      src={getFinalImagePath()}
                      alt="Planner Preview"
                      width={400}
                      height={500}
                      className={styles.previewImage}
                      onError={() => setImageError(true)}
                      onLoad={() => setIsImageLoading(false)}
                      priority
                    />
                  </motion.div>
                ) : (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    whileHover={{ scale: 1.02 }}
                    transition={{ duration: 0.2 }}
                  >
                  </motion.div>
                )}
                <motion.div
                  className={styles.actionButtons}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  <motion.a
                    href={selectedPlanner.Link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={styles.downloadButton}
                    whileHover={{ scale: 1.05, y: -2 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Download Planner
                  </motion.a>
                  <motion.button
                    onClick={handleCopyLink}
                    className={styles.copyButton}
                    whileHover={{ scale: 1.05, y: -2 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {copySuccess ? 'Copied!' : 'Copy Link'}
                  </motion.button>
                  <motion.button
                    onClick={handleReset}
                    className={styles.resetButton}
                    whileHover={{ scale: 1.05, y: -2 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Start Over
                  </motion.button>
                </motion.div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </main>
  );
}