.page {
  --gray-rgb: 0, 0, 0;
  --gray-alpha-200: rgba(var(--gray-rgb), 0.08);
  --gray-alpha-100: rgba(var(--gray-rgb), 0.05);

  --button-primary-hover: #383838;
  --button-secondary-hover: #f2f2f2;

  display: grid;
  grid-template-rows: 20px 1fr 20px;
  align-items: center;
  justify-items: center;
  min-height: 100svh;
  padding: 80px;
  gap: 64px;
  font-family: var(--font-geist-sans);
}

@media (prefers-color-scheme: dark) {
  .page {
    --gray-rgb: 255, 255, 255;
    --gray-alpha-200: rgba(var(--gray-rgb), 0.145);
    --gray-alpha-100: rgba(var(--gray-rgb), 0.06);

    --button-primary-hover: #ccc;
    --button-secondary-hover: #1a1a1a;
  }
}

.main {
  min-height: 100vh;
  padding: 2rem;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #f6f8fd 0%, #f1f4f9 100%);
  overflow-x: hidden;
}

.container {
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(8px);
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.title {
  font-size: 2.5rem;
  background: linear-gradient(135deg, #0070f3 0%, #00a3ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-align: center;
  margin-bottom: 2rem;
  position: relative;
  font-weight: 800;
}

.title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #0070f3, #00a3ff);
  border-radius: 2px;
}

.stepsContainer {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  padding: 1rem;
  margin-bottom: 2rem;
}

.preview {
  margin-top: 2rem;
  padding: 2rem;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 12px;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.preview::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #0070f3, #00a3ff);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.preview:hover::before {
  transform: scaleX(1);
}

.preview:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.preview h2 {
  color: #1a1a1a;
  margin-bottom: 1rem;
  font-size: 1.75rem;
  font-weight: 700;
}

.stepDescription {
  color: #4a5568;
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
  line-height: 1.6;
}

.error {
  margin-top: 1rem;
  padding: 1.25rem;
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  border: 1px solid #ef4444;
  border-radius: 12px;
  color: #dc2626;
  text-align: center;
  font-weight: 500;
  position: relative;
  overflow: hidden;
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.error::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: translateX(-100%);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  100% { transform: translateX(100%); }
}

.result {
  margin-top: 2rem;
  padding: 2rem;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 12px;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.result::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #0070f3, #00a3ff);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.result:hover::before {
  transform: scaleX(1);
}

.result:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.result h2 {
  color: #1a1a1a;
  margin-bottom: 1.5rem;
  font-size: 1.75rem;
  font-weight: 700;
}

.plannerPreview {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
}

.imageContainer {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.imageContainer:hover {
  transform: scale(1.02);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

.previewImage {
  border-radius: 12px;
  max-width: 100%;
  height: auto;
  transition: transform 0.3s ease;
  display: block;
}

.loadingImage {
  width: 400px;
  height: 500px;
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  position: relative;
  overflow: hidden;
}

.loadingImage::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: translateX(-100%);
  animation: shimmer 1.5s infinite;
}

.loadingSpinner {
  width: 48px;
  height: 48px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #0070f3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.fallbackImage {
  width: 400px;
  height: 500px;
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  border: 2px dashed #d1d5db;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  color: #4b5563;
  font-size: 1.1rem;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.fallbackImage:hover {
  border-color: #9ca3af;
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
}

.retryButton {
  padding: 0.875rem 1.75rem;
  background: linear-gradient(135deg, #0070f3 0%, #00a3ff 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 112, 243, 0.2);
}

.retryButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 112, 243, 0.3);
}

.actionButtons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
  width: 100%;
  max-width: 400px;
}

.downloadButton {
  display: inline-block;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #0070f3 0%, #00a3ff 100%);
  color: white;
  text-decoration: none;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  flex: 1;
  min-width: 200px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 112, 243, 0.2);
}

.downloadButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 112, 243, 0.3);
}

.resetButton {
  display: inline-block;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  color: #374151;
  text-decoration: none;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  border: 1px solid #d1d5db;
  cursor: pointer;
  flex: 1;
  min-width: 200px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.resetButton:hover {
  background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.main ol {
  font-family: var(--font-geist-mono);
  padding-left: 0;
  margin: 0;
  font-size: 14px;
  line-height: 24px;
  letter-spacing: -0.01em;
  list-style-position: inside;
}

.main li:not(:last-of-type) {
  margin-bottom: 8px;
}

.main code {
  font-family: inherit;
  background: var(--gray-alpha-100);
  padding: 2px 4px;
  border-radius: 4px;
  font-weight: 600;
}

.ctas {
  display: flex;
  gap: 16px;
}

.ctas a {
  appearance: none;
  border-radius: 128px;
  height: 48px;
  padding: 0 20px;
  border: none;
  border: 1px solid transparent;
  transition:
    background 0.2s,
    color 0.2s,
    border-color 0.2s;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  line-height: 20px;
  font-weight: 500;
}

a.primary {
  background: var(--foreground);
  color: var(--background);
  gap: 8px;
}

a.secondary {
  border-color: var(--gray-alpha-200);
  min-width: 180px;
}

.footer {
  grid-row-start: 3;
  display: flex;
  gap: 24px;
}

.footer a {
  display: flex;
  align-items: center;
  gap: 8px;
}

.footer img {
  flex-shrink: 0;
}

/* Enable hover only on non-touch devices */
@media (hover: hover) and (pointer: fine) {
  a.primary:hover {
    background: var(--button-primary-hover);
    border-color: transparent;
  }

  a.secondary:hover {
    background: var(--button-secondary-hover);
    border-color: transparent;
  }

  .footer a:hover {
    text-decoration: underline;
    text-underline-offset: 4px;
  }
}

@media (max-width: 600px) {
  .page {
    padding: 32px;
    padding-bottom: 80px;
  }

  .main {
    align-items: center;
  }

  .main ol {
    text-align: center;
  }

  .ctas {
    flex-direction: column;
  }

  .ctas a {
    font-size: 14px;
    height: 40px;
    padding: 0 16px;
  }

  a.secondary {
    min-width: auto;
  }

  .footer {
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .main {
    padding: 1rem;
  }

  .container {
    padding: 1.5rem;
    border-radius: 12px;
  }

  .title {
    font-size: 2rem;
  }

  .preview {
    padding: 1.5rem;
    margin-top: 1rem;
  }

  .preview h2 {
    font-size: 1.5rem;
  }

  .stepDescription {
    font-size: 1rem;
  }

  .error {
    padding: 1rem;
    font-size: 0.95rem;
    margin: 0.5rem 0;
  }

  .result {
    padding: 1.5rem;
    margin-top: 1rem;
  }

  .result h2 {
    font-size: 1.5rem;
  }

  .loadingImage,
  .fallbackImage {
    width: 100%;
    height: 300px;
  }

  .actionButtons {
    flex-direction: column;
    gap: 0.75rem;
  }

  .downloadButton,
  .resetButton {
    width: 100%;
    padding: 0.875rem 1.5rem;
    font-size: 0.95rem;
  }

  .stepCounter {
    position: relative;
    top: 0;
    right: 0;
    margin-bottom: 1rem;
    display: inline-block;
  }

  .copyButton {
    width: 100%;
    padding: 0.875rem 1.5rem;
    font-size: 0.95rem;
  }
}

@media (max-width: 480px) {
  .main {
    padding: 0.75rem;
  }

  .container {
    padding: 1rem;
  }

  .title {
    font-size: 1.75rem;
    margin-bottom: 1.5rem;
  }

  .stepsContainer {
    gap: 1rem;
  }

  .preview,
  .result {
    padding: 1.25rem;
  }

  .preview h2,
  .result h2 {
    font-size: 1.25rem;
  }

  .stepDescription {
    font-size: 0.95rem;
  }

  .loadingImage,
  .fallbackImage {
    height: 250px;
  }

  .retryButton {
    padding: 0.75rem 1.5rem;
    font-size: 0.95rem;
  }
}

@media (prefers-color-scheme: dark) {
  .logo {
    filter: invert();
  }
}

.stepCounter {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: linear-gradient(135deg, #0070f3 0%, #00a3ff 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(0, 112, 243, 0.2);
  transition: all 0.3s ease;
}

.stepCounter:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 112, 243, 0.3);
}

.copyButton {
  display: inline-block;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  text-decoration: none;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  flex: 1;
  min-width: 200px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
}

.copyButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(16, 185, 129, 0.3);
}
