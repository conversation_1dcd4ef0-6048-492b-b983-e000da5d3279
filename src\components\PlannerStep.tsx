import { motion } from 'framer-motion';
import styles from './PlannerStep.module.css';

interface PlannerStepProps {
  stepNumber: number;
  title: string;
  description: string;
  isSelected: boolean;
  isCompleted: boolean;
  isLocked: boolean;
}

export default function PlannerStep({
  stepNumber,
  title,
  description,
  isSelected,
  isCompleted,
  isLocked,
}: PlannerStepProps) {
  const getStepState = () => {
    if (isCompleted) return 'completed';
    if (isSelected) return 'current';
    if (isLocked) return 'locked';
    return 'available';
  };

  const stepState = getStepState();

  return (
    <motion.div
      className={`${styles.step} ${styles[stepState]}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <motion.div
        className={`${styles.stepNumber} ${styles[`${stepState}Number`]}`}
        animate={{
          scale: isSelected ? 1.1 : 1,
        }}
      >
        {isCompleted ? '✓' : stepNumber}
      </motion.div>
      <h3 className={`${styles.stepTitle} ${styles[`${stepState}Title`]}`}>{title}</h3>
      <p className={`${styles.stepDescription} ${styles[`${stepState}Description`]}`}>{description}</p>
    </motion.div>
  );
}