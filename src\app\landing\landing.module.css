.main {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;
  position: relative;
}

.main::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
  pointer-events: none;
}

.container {
  max-width: 1200px;
  width: 95%;
  position: relative;
  z-index: 1;
}

.content {
  text-align: center;
  padding: 3rem;
  background: rgba(255, 255, 255, 0.98);
  border-radius: 24px;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
  position: relative;
  transition: all 0.3s ease;
}

.content:hover {
  box-shadow:
    0 30px 60px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.3);
}

.content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
}

.title {
  font-size: 3.5rem;
  font-weight: 900;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1.5rem;
  letter-spacing: -0.02em;
  line-height: 1.1;
}

.description {
  font-size: 1.3rem;
  color: #64748b;
  font-weight: 500;
  max-width: 650px;
  margin: 0 auto 2.5rem;
  line-height: 1.7;
  opacity: 0.9;
}

.ctaButton {
  display: inline-block;
  padding: 1.25rem 3rem;
  font-size: 1.25rem;
  font-weight: 700;
  color: white;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  text-decoration: none;
  box-shadow:
    0 8px 20px rgba(102, 126, 234, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  letter-spacing: 0.025em;
}

.ctaButton:hover {
  transform: translateY(-3px);
  box-shadow:
    0 12px 30px rgba(102, 126, 234, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.3);
}

.decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  overflow: hidden;
}

.circle1 {
  position: absolute;
  top: -100px;
  right: -100px;
  width: 300px;
  height: 300px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
  animation: float 6s ease-in-out infinite;
  filter: blur(1px);
}

.circle2 {
  position: absolute;
  bottom: -150px;
  left: -150px;
  width: 400px;
  height: 400px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(118, 75, 162, 0.15) 0%, rgba(240, 147, 251, 0.15) 100%);
  animation: float 8s ease-in-out infinite;
  filter: blur(1px);
}

.circle3 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(240, 147, 251, 0.1) 100%);
  animation: float 7s ease-in-out infinite;
  filter: blur(0.5px);
}

@keyframes float {
  0% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(5deg);
  }
  100% {
    transform: translateY(0) rotate(0deg);
  }
}

@media (max-width: 768px) {
  .main {
    padding: 1rem;
  }

  .content {
    padding: 2rem;
  }

  .title {
    font-size: 2.5rem;
  }

  .description {
    font-size: 1.1rem;
    margin-bottom: 2rem;
  }

  .ctaButton {
    padding: 1rem 2.5rem;
    font-size: 1.1rem;
  }

  .circle1,
  .circle2,
  .circle3 {
    opacity: 0.5;
  }
}

@media (max-width: 480px) {
  .main {
    padding: 0.5rem;
  }

  .content {
    padding: 1.5rem;
  }

  .title {
    font-size: 2rem;
  }

  .description {
    font-size: 1rem;
    margin-bottom: 1.5rem;
  }

  .ctaButton {
    padding: 0.875rem 2rem;
    font-size: 1rem;
  }
}