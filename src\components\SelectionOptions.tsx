import { motion } from 'framer-motion';
import Image from 'next/image';
import styles from './SelectionOptions.module.css';

interface SelectionOptionsProps {
  options: string[];
  onSelect: (option: string) => void;
  selectedOption: string | null;
  getOptionImagePath: (option: string) => string;
}

export default function SelectionOptions({ 
  options, 
  onSelect, 
  selectedOption,
  getOptionImagePath 
}: SelectionOptionsProps) {
  return (
    <div className={styles.optionsContainer}>
      {options.map((option) => (
        <motion.button
          key={option}
          className={`${styles.option} ${selectedOption === option ? styles.selected : ''}`}
          onClick={() => onSelect(option)}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.2 }}
        >
          <div className={styles.optionContent}>
            <div className={styles.optionImage}>
              <Image
                src={getOptionImagePath(option)}
                alt={`${option} preview`}
                width={200}
                height={250}
                className={styles.previewImage}
              />
            </div>
            <span className={styles.optionText}>{option}</span>
          </div>
        </motion.button>
      ))}
    </div>
  );
} 