import { motion } from 'framer-motion';
import styles from './ProgressBar.module.css';

interface ProgressBarProps {
  currentStep: number;
  totalSteps: number;
  completedSteps: number;
  stepTitles?: string[];
}

export default function ProgressBar({ currentStep, totalSteps, completedSteps, stepTitles = [] }: ProgressBarProps) {
  return (
    <div className={styles.progressContainer}>
      <div className={styles.stepsWrapper}>
        {Array.from({ length: totalSteps }, (_, index) => {
          const stepNumber = index + 1;
          const isCompleted = stepNumber <= completedSteps;
          const isCurrent = stepNumber === currentStep;
          const isPending = stepNumber > currentStep;

          return (
            <div key={stepNumber} className={styles.stepContainer}>
              <div className={styles.stepIndicator}>
                <motion.div
                  className={`${styles.stepCircle} ${
                    isCompleted ? styles.completed :
                    isCurrent ? styles.current :
                    styles.pending
                  }`}
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: index * 0.1 }}
                >
                  {isCompleted ? '✓' : isPending ? '×' : '✓'}
                </motion.div>

                {/* Connection line to next step */}
                {index < totalSteps - 1 && (
                  <div className={`${styles.connectionLine} ${
                    stepNumber <= completedSteps ? styles.lineCompleted : styles.linePending
                  }`} />
                )}
              </div>

              <div className={styles.stepLabel}>
                <span className={`${styles.stepTitle} ${
                  isCompleted ? styles.titleCompleted :
                  isCurrent ? styles.titleCurrent :
                  styles.titlePending
                }`}>
                  {stepTitles?.[index] || `Step ${stepNumber}`}
                </span>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
