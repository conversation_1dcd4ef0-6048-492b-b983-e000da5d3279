.main {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem 0;
  overflow-x: hidden;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  position: relative;
}

.main::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
  pointer-events: none;
}

.container {
  max-width: 1200px;
  width: 95%;
  min-height: 90vh;
  margin: 0 auto;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.98);
  border-radius: 24px;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  position: relative;
  transition: all 0.3s ease;
}

.container:hover {
  box-shadow:
    0 30px 60px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.3);
}

.header {
  text-align: center;
  padding: 0;
  margin: 0;
}

.title {
  font-size: 2.5rem;
  font-weight: 900;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0 0 0.5rem 0;
  letter-spacing: -0.02em;
  line-height: 1.1;
}

.subtitle {
  font-size: 1.1rem;
  color: #64748b;
  font-weight: 500;
  margin: 0;
  opacity: 0.8;
}

.contentArea {
  display: grid;
  grid-template-columns: 1fr 320px;
  gap: 2rem;
  flex: 1;
}

.mainContent {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.sidebar {
  display: flex;
  flex-direction: column;
}

.lockedMessage {
  background: linear-gradient(145deg, #f8fafc 0%, #f1f5f9 100%);
  border: 2px solid rgba(226, 232, 240, 0.8);
  border-radius: 16px;
  padding: 2.5rem;
  text-align: center;
  color: #64748b;
  font-style: italic;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  position: relative;
}

.lockedMessage::before {
  content: '🔒';
  font-size: 2rem;
  display: block;
  margin-bottom: 1rem;
}

.lockedHint {
  margin-top: 0.75rem;
  font-size: 0.9rem;
  color: #94a3b8;
  font-weight: 500;
}

.selectionsummary {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  padding: 1.5rem;
  border-radius: 20px;
  box-shadow:
    0 10px 25px rgba(0, 0, 0, 0.08),
    0 0 0 1px rgba(255, 255, 255, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  height: fit-content;
  position: relative;
}

.selectionsummary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
}

.selectionsummary h3 {
  margin: 0 0 1.5rem 0;
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 700;
  letter-spacing: -0.025em;
}

.selectionsList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.selectionItem {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 1rem;
  background: linear-gradient(145deg, #f8fafc 0%, #ffffff 100%);
  border-radius: 12px;
  border: 1px solid rgba(226, 232, 240, 0.8);
  transition: all 0.2s ease;
}

.selectionItem:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.selectionLabel {
  font-weight: 600;
  color: #64748b;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.selectionValue {
  color: #059669;
  font-weight: 700;
  font-size: 1rem;
  margin: 0;
}

.changeButton {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
  align-self: flex-start;
}

.changeButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.preview {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  padding: 2rem;
  border-radius: 20px;
  box-shadow:
    0 10px 25px rgba(0, 0, 0, 0.08),
    0 0 0 1px rgba(255, 255, 255, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  position: relative;
  min-height: 400px;
}

.preview::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
}

.stepCounter {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 25px;
  font-weight: 600;
  font-size: 0.875rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  letter-spacing: 0.025em;
}

.stepDescription {
  color: #64748b;
  margin: 1rem 0 2rem;
  line-height: 1.7;
  font-size: 1rem;
  font-weight: 400;
}

.preview h2 {
  color: #1e293b;
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
  letter-spacing: -0.025em;
}

.error {
  background: #ff6b6b;
  color: white;
  padding: 1rem;
  border-radius: 10px;
  margin: 1rem 0;
  text-align: center;
}

.result {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  padding: 2rem;
  border-radius: 20px;
  box-shadow:
    0 10px 25px rgba(0, 0, 0, 0.08),
    0 0 0 1px rgba(255, 255, 255, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
  margin-top: 2rem;
  position: relative;
}

.result::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
}

.result h2 {
  color: #1e293b;
  font-size: 2rem;
  font-weight: 800;
  margin: 0 0 1.5rem 0;
  letter-spacing: -0.025em;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.plannerPreview {
  margin-top: 2rem;
}

.loadingImage {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.loadingSpinner {
  width: 50px;
  height: 50px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid #4ECDC4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.imageContainer {
  margin: 2rem auto;
  max-width: 400px;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.previewImage {
  width: 100%;
  height: auto;
  object-fit: cover;
}

.fallbackImage {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 10px;
  margin: 2rem auto;
  max-width: 400px;
}

.retryButton {
  background: #4ECDC4;
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  margin-top: 1rem;
  transition: all 0.3s ease;
}

.retryButton:hover {
  background: #45b7af;
  transform: translateY(-2px);
}

.actionButtons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
  flex-wrap: wrap;
}

.downloadButton,
.copyButton,
.resetButton {
  padding: 0.875rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  border: none;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.downloadButton {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  text-decoration: none;
}

.copyButton {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.resetButton {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.downloadButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(16, 185, 129, 0.4);
}

.copyButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
}

.resetButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(239, 68, 68, 0.4);
}

@media (max-width: 1024px) {
  .contentArea {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .sidebar {
    order: -1;
  }
}

@media (max-width: 768px) {
  .main {
    padding: 1rem 0.5rem;
  }

  .container {
    padding: 1.5rem;
    min-height: auto;
    gap: 1rem;
    width: 98%;
  }

  .title {
    font-size: 2rem;
  }

  .subtitle {
    font-size: 1rem;
  }

  .preview {
    padding: 1.5rem;
    min-height: 300px;
  }

  .actionButtons {
    flex-direction: column;
  }

  .downloadButton,
  .copyButton,
  .resetButton {
    width: 100%;
  }

  .selectionItem {
    padding: 0.75rem;
  }

  .selectionsummary {
    padding: 1rem;
  }

  .selectionsummary h3 {
    font-size: 1.125rem;
    margin-bottom: 1rem;
  }
}

@media (max-width: 480px) {
  .main {
    padding: 0.5rem;
  }

  .container {
    padding: 1rem;
    gap: 0.75rem;
    width: 99%;
  }

  .title {
    font-size: 1.75rem;
  }

  .subtitle {
    font-size: 0.9rem;
  }

  .preview {
    padding: 1rem;
    min-height: 250px;
  }

  .selectionsummary {
    padding: 0.75rem;
  }

  .selectionItem {
    padding: 0.5rem;
  }
}