/* Base step styles */
.step {
  background-color: white;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  cursor: default;
}

/* Step states */
.completed {
  border-color: #10b981;
  background-color: #f0fdf4;
}

.current {
  border-color: #3b82f6;
  background-color: #eff6ff;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.locked {
  border-color: #d1d5db;
  background-color: #f9fafb;
  opacity: 0.6;
}

.available {
  border-color: #e0e0e0;
  background-color: white;
}

/* Step number styles */
.stepNumber {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.completedNumber {
  background-color: #10b981;
  color: white;
}

.currentNumber {
  background-color: #3b82f6;
  color: white;
}

.lockedNumber {
  background-color: #9ca3af;
  color: white;
}

.availableNumber {
  background-color: #e5e7eb;
  color: #6b7280;
}

/* Title styles */
.stepTitle {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
  padding-right: 2rem;
  transition: color 0.3s ease;
}

.completedTitle {
  color: #059669;
}

.currentTitle {
  color: #1d4ed8;
  font-weight: 600;
}

.lockedTitle {
  color: #9ca3af;
}

.availableTitle {
  color: #374151;
}

/* Description styles */
.stepDescription {
  font-size: 0.9rem;
  line-height: 1.5;
  transition: color 0.3s ease;
}

.completedDescription {
  color: #065f46;
}

.currentDescription {
  color: #1e40af;
}

.lockedDescription {
  color: #9ca3af;
}

.availableDescription {
  color: #6b7280;
}

@media (max-width: 768px) {
  .step {
    padding: 1rem;
  }

  .stepTitle {
    font-size: 1.1rem;
  }

  .stepDescription {
    font-size: 0.85rem;
  }
}