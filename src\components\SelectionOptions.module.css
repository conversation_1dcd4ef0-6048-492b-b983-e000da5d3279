.optionsContainer {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  width: 100%;
  padding: 1rem;
}

.option {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
  text-align: center;
  overflow: hidden;
}

.option:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transform: translateY(-2px);
}

.option.selected {
  border-color: #3b82f6;
  background-color: #eff6ff;
  box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.2);
}

.optionContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
}

.optionImage {
  width: 100%;
  height: 300px;
  position: relative;
  border-radius: 4px;
  overflow: hidden;
  background-color: #f8fafc;
}

.previewImage {
  object-fit: contain;
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

.option:hover .previewImage {
  transform: scale(1.05);
}

.optionText {
  font-size: 1.1rem;
  font-weight: 500;
  color: #1f2937;
  padding: 0.5rem 0;
}

@media (max-width: 768px) {
  .optionsContainer {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }

  .optionImage {
    height: 250px;
  }

  .optionText {
    font-size: 1rem;
  }
} 